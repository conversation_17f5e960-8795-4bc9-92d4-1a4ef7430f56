# Backend Checklist para Corrective Maintenance Progress

## 1. Verificar que existe el endpoint

Busca en tu backend el archivo que maneja:
```
PATCH /vendor-platform/corrective-maintenance/services/:serviceId/progress
```

Debería estar en algo como:
- `routes/vendor-platform/corrective-maintenance/services.js`
- `controllers/correctiveMaintenanceController.js`
- `src/routes/corrective-maintenance/services/progress.js`

## 2. Verificar configuración de multipart

El endpoint debe estar configurado para manejar `multipart/form-data`:

```javascript
// Ejemplo con Express + Multer
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });

router.patch('/services/:serviceId/progress', upload.array('evidence'), async (req, res) => {
  try {
    const { serviceId } = req.params;
    const { status, notes } = req.body;
    const evidenceFiles = req.files; // Array de archivos
    
    console.log('Received data:', {
      serviceId,
      status,
      notes,
      evidenceCount: evidenceFiles?.length || 0
    });
    
    // Tu lógica aquí...
    
  } catch (error) {
    console.error('Error updating service progress:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Error al actualizar progreso del servicio' 
    });
  }
});
```

## 3. Verificar estructura de respuesta

El frontend espera esta estructura de respuesta:

```javascript
// Respuesta exitosa
{
  "success": true,
  "data": {
    "_id": "serviceId",
    "status": "completed",
    "evidence": [
      {
        "type": "photo",
        "url": "https://...",
        "uploadedAt": "2024-01-01T00:00:00Z"
      }
    ]
    // ... otros campos del servicio
  },
  "message": "Progreso del servicio actualizado exitosamente"
}

// Respuesta de error
{
  "success": false,
  "message": "Descripción del error",
  "details": "Detalles adicionales del error"
}
```

## 4. Verificar autenticación

El endpoint debe validar el token Bearer:

```javascript
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ 
      success: false, 
      message: 'Token de acceso requerido' 
    });
  }
  
  // Validar token...
  next();
};
```

## 5. Verificar permisos de usuario

```javascript
// Verificar que el usuario puede actualizar servicios
if (!user.canUpdateServices || user.userType !== 'workshop') {
  return res.status(403).json({
    success: false,
    message: 'Sin permisos para actualizar servicios'
  });
}
```

## 6. Verificar validaciones de negocio

```javascript
// Validar que el servicio existe
const service = await Service.findById(serviceId);
if (!service) {
  return res.status(404).json({
    success: false,
    message: 'Servicio no encontrado'
  });
}

// Validar que se puede cambiar el estado
if (service.status === 'completed' && status === 'completed') {
  return res.status(400).json({
    success: false,
    message: 'El servicio ya está completado'
  });
}

// Validar evidencia requerida para completar
if (status === 'completed' && (!evidenceFiles || evidenceFiles.length === 0)) {
  return res.status(400).json({
    success: false,
    message: 'La evidencia es obligatoria para completar el servicio'
  });
}
```

## 7. Verificar procesamiento de archivos

```javascript
// Procesar archivos de evidencia
const processedEvidence = [];

if (evidenceFiles && evidenceFiles.length > 0) {
  for (const file of evidenceFiles) {
    // Validar tipo de archivo
    if (!file.mimetype.startsWith('image/') && !file.mimetype.startsWith('video/')) {
      return res.status(400).json({
        success: false,
        message: `Tipo de archivo no válido: ${file.originalname}`
      });
    }
    
    // Validar tamaño (10MB)
    if (file.size > 10 * 1024 * 1024) {
      return res.status(400).json({
        success: false,
        message: `Archivo muy grande: ${file.originalname}`
      });
    }
    
    // Subir a storage (S3, local, etc.)
    const fileUrl = await uploadFile(file);
    
    processedEvidence.push({
      type: file.mimetype.startsWith('image/') ? 'photo' : 'video',
      url: fileUrl,
      originalName: file.originalname,
      uploadedAt: new Date()
    });
  }
}
```

## 8. Comandos para debuggear

```bash
# Ver logs del backend
tail -f logs/app.log

# Verificar que el endpoint existe
curl -X PATCH http://localhost:3000/vendor-platform/corrective-maintenance/services/test/progress \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "status=in-progress" \
  -F "notes=test"

# Verificar estructura de rutas
grep -r "services.*progress" src/routes/
```

## 9. Posibles errores comunes

1. **Endpoint no existe**: 404 Not Found
2. **Multer no configurado**: Error al procesar archivos
3. **CORS**: Error de CORS en el navegador
4. **Token inválido**: 401 Unauthorized
5. **Permisos**: 403 Forbidden
6. **Validación**: 400 Bad Request
7. **Storage**: Error al subir archivos

## 10. Logs a revisar

Busca en los logs del backend:
- Requests entrantes al endpoint
- Errores de multer/file upload
- Errores de base de datos
- Errores de autenticación
