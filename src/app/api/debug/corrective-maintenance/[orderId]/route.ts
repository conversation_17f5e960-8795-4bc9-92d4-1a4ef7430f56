import { NextRequest, NextResponse } from 'next/server';
import { URL_API } from '@/constants';
import getCurrentUser from '@/actions/getCurrentUser';

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    const { orderId } = params;

    console.log('🔍 DEBUG - Starting debug check for order:', orderId);

    // Test 1: Direct backend call
    console.log('🔍 DEBUG - Test 1: Direct backend call');
    const backendUrl = `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}`;
    console.log('🔍 DEBUG - Backend URL:', backendUrl);

    const backendResponse = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
    });

    console.log('🔍 DEBUG - Backend response status:', backendResponse.status);

    if (!backendResponse.ok) {
      const errorText = await backendResponse.text();
      console.error('🔍 DEBUG - Backend error:', errorText);
      return NextResponse.json({
        success: false,
        message: 'Backend error',
        error: errorText,
        status: backendResponse.status
      });
    }

    const backendData = await backendResponse.json();
    
    console.log('🔍 DEBUG - Raw backend response:', JSON.stringify(backendData, null, 2));

    // Test 2: Check specific service
    const services = backendData.data?.services || [];
    console.log('🔍 DEBUG - Services found:', services.length);

    const debugInfo = {
      orderId,
      timestamp: new Date().toISOString(),
      backendUrl,
      responseStatus: backendResponse.status,
      orderData: {
        id: backendData.data?._id,
        status: backendData.data?.status,
        servicesCount: services.length
      },
      services: services.map((service: any) => ({
        id: service._id,
        name: service.serviceName,
        status: service.status,
        evidenceCount: service.evidence?.length || 0,
        evidenceArray: service.evidence || [],
        hasEvidenceProperty: service.hasOwnProperty('evidence'),
        evidenceType: typeof service.evidence,
        rawEvidence: JSON.stringify(service.evidence)
      })),
      rawBackendResponse: backendData
    };

    console.log('🔍 DEBUG - Complete debug info:', JSON.stringify(debugInfo, null, 2));

    return NextResponse.json({
      success: true,
      debug: debugInfo,
      message: 'Debug information collected'
    });

  } catch (error: any) {
    console.error('🔍 DEBUG - Error during debug:', error);
    return NextResponse.json({
      success: false,
      message: 'Debug error',
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
