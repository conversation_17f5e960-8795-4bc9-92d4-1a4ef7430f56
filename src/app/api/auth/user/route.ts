import { NextRequest, NextResponse } from 'next/server';
import getCurrentUser from '@/actions/getCurrentUser';

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      accessToken: user.accessToken,
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        userType: user.userType,
      },
    });
  } catch (error: any) {
    console.error('❌ Error getting current user:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Error al obtener información del usuario',
      },
      { status: 500 }
    );
  }
}
