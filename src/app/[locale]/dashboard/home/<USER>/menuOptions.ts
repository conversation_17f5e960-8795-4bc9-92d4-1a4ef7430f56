import { FiHome } from 'react-icons/fi';
import { Bi<PERSON>ser, BiCar, BiFolderOpen, BiCalendar, BiSolidCity, BiListPlus } from 'react-icons/bi';
import { LuWrench } from 'react-icons/lu';
import { TbReportAnalytics } from 'react-icons/tb';
import { FaFileInvoice, FaCarSide } from 'react-icons/fa';
import { CheckCircle } from 'lucide-react';
import { CompanyUserRole } from '@/lib/user-types';
import { getCompanyLinks } from '@/components/Sidebar/CompanyLinks';

// Import the existing navigation items from SideNavbar
const WorkshopLinkItems = [
  // Servicios
  { name: "Services", icon: LuWrench, link: "/dashboard/services", category: "Servicios" },
  { name: "Calendar", icon: BiCalendar, link: "/dashboard/appointments", category: "Servicios" },
  { name: "Corrective Maintenance", icon: <PERSON><PERSON><PERSON>, link: "/dashboard/corrective-maintenance", category: "Servicios" },

  // Vehículos
  { name: "Car", icon: BiCar, link: "/dashboard/vehicles", category: "Vehículos" },
];

const GestorLinkItems = [
  // Trámites
  { name: "Trámites", icon: BiFolderOpen, link: "/dashboard/gestor/tramites", category: "Trámites" },
];

// Define descriptions for each menu item
const menuDescriptions: Record<string, string> = {
  // Workshop descriptions
  "Services": "Administración de servicios",
  "Calendar": "Agenda de citas",
  "User": "Gestión de usuarios",

  // Gestor descriptions
  "Inicio": "Panel principal de gestor",
  "Trámites": "Gestión de trámites",

  // Company descriptions
  "Users": "Gestión de usuarios de la compañía",
  "Cities": "Administración de ciudades",
  "Installations": "Calendario de instalaciones",

  // Admin only descriptions
  "Configuración": "Configuración del sistema",
  "Flotilla": "Administración de flotilla",
  "Facturación": "Sistema de facturación",
  "Crear Trámites": "Creación y gestión de trámites",
  "Aprobación de Cotizaciones": "Aprobación y gestión de cotizaciones"
};

export interface MenuOption {
  title: string;
  description: string;
  icon: any;
  link: string;
  color?: string;
  category?: string;
}

// Additional options only available to superAdmin
export const adminOnlyOptions: MenuOption[] = [
  {
    title: "Configuración",
    description: menuDescriptions["Configuración"],
    icon: BiUser,
    link: "/dashboard/settings",
    color: "bg-red-600",
    category: "Admin"
  },
  {
    title: "Flotilla",
    description: menuDescriptions["Flotilla"],
    icon: FaCarSide,
    link: "/dashboard/flotilla",
    color: "bg-red-700",
    category: "Admin"
  },
  {
    title: "Facturación",
    description: menuDescriptions["Facturación"],
    icon: FaFileInvoice,
    link: "/dashboard/invoicing",
    color: "bg-red-800",
    category: "Admin"
  },
  {
    title: "Crear Trámites",
    description: menuDescriptions["Crear Trámites"],
    icon: BiListPlus,
    link: "/dashboard/administrador/gestores/tramites",
    color: "bg-red-500",
    category: "Admin"
  },
  {
    title: "Aprobación de Cotizaciones",
    description: menuDescriptions["Aprobación de Cotizaciones"],
    icon: CheckCircle,
    link: "/dashboard/administrador/aprobacion-cotizaciones",
    color: "bg-red-600",
    category: "Admin"
  },
];

// Define color schemes for different user types
const colorSchemes = {
  workshop: [
    "bg-blue-600",
    "bg-blue-700",
    "bg-blue-800",
    "bg-blue-500",
    "bg-blue-400",
    "bg-blue-900"
  ],
  company: [
    "bg-green-600",
    "bg-green-700",
    "bg-green-800",
    "bg-green-500",
    "bg-green-400",
    "bg-green-900"
  ],
  gestor: [
    "bg-purple-600",
    "bg-purple-700",
    "bg-purple-800",
    "bg-purple-500",
    "bg-purple-400",
    "bg-purple-900"
  ],
  admin: [
    "bg-red-600",
    "bg-red-700",
    "bg-red-800",
    "bg-red-500",
    "bg-red-400",
    "bg-red-900"
  ]
};

// Convert the existing link items to our MenuOption format
const convertToMenuOptions = (linkItems: any[], userType: 'workshop' | 'company' | 'gestor' | 'admin' = 'workshop'): MenuOption[] => {
  const colors = colorSchemes[userType];

  return linkItems.map((item, index) => ({
    title: item.name,
    description: menuDescriptions[item.name] || `Acceso a ${item.name}`,
    icon: item.icon,
    link: item.link,
    color: colors[index % colors.length],
    category: item.category || 'General'
  }));
};

// Get options based on user type
export const getMenuOptionsByUserType = (userType: string, isSuperAdmin: boolean = false) => {
  // Get the appropriate link items based on user type
  let linkItems: any[] = [];

  // Function to filter out dashboard-related options
  const filterDashboardOptions = (options: MenuOption[]) => {
    return options.filter(option =>
      !option.link.includes('/dashboard/home') &&
      !option.title.toLowerCase().includes('dashboard') &&
      !option.title.toLowerCase().includes('inicio')
    );
  };

  if (isSuperAdmin) {
    // SuperAdmin gets all options
    const workshopOptions = convertToMenuOptions(WorkshopLinkItems, 'workshop');
    const gestorOptions = convertToMenuOptions(GestorLinkItems, 'gestor');
    const companyOptions = convertToMenuOptions(getCompanyLinks(CompanyUserRole.OWNER), 'company');
    const adminOptions = adminOnlyOptions; // Admin-specific options with red color scheme

    // Filter out dashboard options
    return filterDashboardOptions([
      ...workshopOptions,
      ...companyOptions,
      ...gestorOptions,
      ...adminOptions
    ]);
  }

  switch (userType) {
    case 'workshop':
      linkItems = WorkshopLinkItems;
      return filterDashboardOptions(convertToMenuOptions(linkItems, 'workshop'));
    case 'company':
      // For company users, we need to use the getCompanyLinks function
      // We'll assume OWNER role for maximum access in the home screen
      linkItems = getCompanyLinks(CompanyUserRole.OWNER);
      return filterDashboardOptions(convertToMenuOptions(linkItems, 'company'));
    case 'gestor':
      linkItems = GestorLinkItems;
      return filterDashboardOptions(convertToMenuOptions(linkItems, 'gestor'));
    case 'company-gestor':
      // For company-gestor users, combine company and gestor options
      const companyItems = getCompanyLinks(CompanyUserRole.OWNER);
      const gestorItems = GestorLinkItems;

      // Convert company items with company color scheme
      const companyOptions = convertToMenuOptions(companyItems, 'company');
      // Convert gestor items with gestor color scheme
      const gestorOptions = convertToMenuOptions(gestorItems, 'gestor');

      // Return combined options, filtering out dashboard options
      return filterDashboardOptions([...companyOptions, ...gestorOptions]);
    default:
      linkItems = WorkshopLinkItems; // Default to workshop options
      return filterDashboardOptions(convertToMenuOptions(linkItems, 'workshop'));
  }
};
