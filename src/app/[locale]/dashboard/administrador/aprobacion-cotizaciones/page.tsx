import React from 'react';
import AprobacionCotizacionesClient from './client';
import { getQuotedOrders } from './_actions/getQuotedOrders';
import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';

export default async function AprobacionCotizacionesPage() {
  const user = await getCurrentUser();

  // Check if user is superAdmin
  const isSuperAdmin = user?.userType === 'superAdmin' || 
                       user?.role === 'administrador' || 
                       user?.role === 'superadmin';

  if (!user || !isSuperAdmin) {
    redirect('/dashboard/home');
  }

  const quotedOrdersData = await getQuotedOrders({
    page: 1,
    limit: 50
  });

  return (
    <section className="flex flex-col min-h-[90vh]">
      <div className="pb-4">
        <h1 className="font-bold text-3xl">Aprobación de Cotizaciones</h1>
        <p className="text-gray-600 mt-2">
          Gestiona las cotizaciones pendientes de aprobación
        </p>
      </div>

      <AprobacionCotizacionesClient
        initialData={quotedOrdersData}
      />
    </section>
  );
}