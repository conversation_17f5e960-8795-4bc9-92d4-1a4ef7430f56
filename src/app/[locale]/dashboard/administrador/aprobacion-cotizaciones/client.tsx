'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { CheckCircle, Clock, FileText, Car, AlertTriangle, ArrowRight, Loader2, RefreshCw } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { getStatusLabel, getStatusColor, formatCurrency, type CorrectiveMaintenanceOrder, type ApiResponse } from '@/app/[locale]/dashboard/(workshop-user-routes)/corrective-maintenance/types';

interface AprobacionCotizacionesClientProps {
  initialData: ApiResponse<CorrectiveMaintenanceOrder[]>;
}

export default function AprobacionCotizacionesClient({ initialData }: AprobacionCotizacionesClientProps) {
  const [orders, setOrders] = useState<CorrectiveMaintenanceOrder[]>(initialData.data || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(initialData.success ? null : initialData.message);
  const { toast } = useToast();
  const router = useRouter();

  const handleViewOrder = (orderId: string) => {
    router.push(`/dashboard/corrective-maintenance/${orderId}`);
  };

  const handleRefresh = () => {
    setLoading(true);
    router.refresh();
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Error al cargar datos</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Reintentar
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          {orders.length} cotización{orders.length !== 1 ? 'es' : ''} pendiente{orders.length !== 1 ? 's' : ''} de aprobación
        </div>
        <Button 
          onClick={handleRefresh}
          disabled={loading}
          variant="outline"
          size="sm"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Actualizar
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Cotizaciones</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orders.length}</div>
            <p className="text-xs text-muted-foreground">
              Pendientes de aprobación
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Valor Total</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                orders.reduce((total, order) => total + (order.totalEstimatedCost || 0), 0)
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              En cotizaciones pendientes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Promedio</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {orders.length > 0 
                ? formatCurrency(
                    orders.reduce((total, order) => total + (order.totalEstimatedCost || 0), 0) / orders.length
                  )
                : formatCurrency(0)
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Por cotización
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Orders List */}
      {orders.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No hay cotizaciones pendientes</h3>
              <p className="text-gray-600">
                Todas las cotizaciones han sido procesadas o no hay órdenes cotizadas en este momento.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {orders.map((order) => (
            <Card key={order._id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <CardTitle className="text-lg">
                        Orden #{order._id.slice(-8).toUpperCase()}
                      </CardTitle>
                      <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
                        {order.vehicle && (
                          <div className="flex items-center">
                            <Car className="h-4 w-4 mr-1" />
                            {order.vehicle.brand} {order.vehicle.model} {order.vehicle.year}
                            {order.vehicle.carPlates?.plates && (
                              <span className="ml-2 text-gray-400">
                                • {order.vehicle.carPlates.plates}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(order.status, 'order')} variant="secondary">
                      {getStatusLabel(order.status, 'order')}
                    </Badge>
                    <Button
                      onClick={() => handleViewOrder(order._id)}
                      size="sm"
                    >
                      Revisar y Aprobar
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Taller</p>
                    <p className="text-sm">{order.workshop?.name || 'No especificado'}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Fecha de Creación</p>
                    <p className="text-sm">
                      {format(new Date(order.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Servicios</p>
                    <p className="text-sm">{order.services?.length || 0} servicios</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Costo Total</p>
                    <p className="text-sm font-semibold text-green-600">
                      {formatCurrency(order.totalEstimatedCost || 0)}
                    </p>
                  </div>
                </div>
                
                {order.customerDescription && (
                  <div className="mt-4 pt-4 border-t">
                    <p className="text-sm font-medium text-gray-500 mb-1">Descripción del Cliente</p>
                    <p className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                      {order.customerDescription}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}