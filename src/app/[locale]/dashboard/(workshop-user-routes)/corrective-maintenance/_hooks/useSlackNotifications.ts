'use client';

import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import {
  sendFleetQuotationNotification,
  sendServiceCompletionNotification,
  sendDailyQuotationSummary,
  SlackNotificationResponse
} from '../_services/slackNotifications';
import { CorrectiveMaintenanceOrder, Quotation } from '../types';

export function useSlackNotifications() {
  const { toast } = useToast();
  const [isSending, setIsSending] = useState(false);

  /**
   * Send notification for fleet quotation
   */
  const notifyFleetQuotation = useCallback(async (
    order: CorrectiveMaintenanceOrder,
    quotation: Quotation,
    type: 'pending' | 'approved' | 'rejected' = 'pending',
    showToast: boolean = true
  ): Promise<SlackNotificationResponse> => {
    setIsSending(true);

    try {
      const response = await sendFleetQuotationNotification(order, quotation, type);

      if (response.success && showToast) {
        toast({
          title: 'Notificación enviada',
          description: 'La notificación ha sido enviada a Slack exitosamente',
        });
      } else if (!response.success && showToast) {
        toast({
          title: 'Error en notificación',
          description: response.error || 'No se pudo enviar la notificación a Slack',
          variant: 'destructive',
        });
      }

      return response;
    } catch (error) {
      console.error('Error sending Slack notification:', error);

      if (showToast) {
        toast({
          title: 'Error inesperado',
          description: 'Error inesperado al enviar notificación a Slack',
          variant: 'destructive',
        });
      }

      return {
        success: false,
        error: 'Error inesperado al enviar notificación',
      };
    } finally {
      setIsSending(false);
    }
  }, [toast]);

  /**
   * Send notification when a service is completed
   */
  const notifyServiceCompletion = useCallback(async (
    order: CorrectiveMaintenanceOrder,
    serviceName: string,
    completionDetails: {
      actualDuration: number;
      estimatedDuration: number;
      totalCost: number;
      evidenceCount: number;
    },
    showToast: boolean = true
  ): Promise<SlackNotificationResponse> => {
    setIsSending(true);

    try {
      const response = await sendServiceCompletionNotification(order, serviceName, completionDetails);

      if (response.success && showToast) {
        toast({
          title: 'Notificación de finalización enviada',
          description: 'Se ha notificado la finalización del servicio a Slack',
        });
      } else if (!response.success && showToast) {
        toast({
          title: 'Error en notificación',
          description: response.error || 'No se pudo enviar la notificación de finalización',
          variant: 'destructive',
        });
      }

      return response;
    } catch (error) {
      console.error('Error sending service completion notification:', error);

      if (showToast) {
        toast({
          title: 'Error inesperado',
          description: 'Error inesperado al enviar notificación de finalización',
          variant: 'destructive',
        });
      }

      return {
        success: false,
        error: 'Error inesperado al enviar notificación',
      };
    } finally {
      setIsSending(false);
    }
  }, [toast]);

  /**
   * Send daily summary of pending quotations
   */
  const sendDailySummary = useCallback(async (
    showToast: boolean = true
  ): Promise<SlackNotificationResponse> => {
    setIsSending(true);

    try {
      const response = await sendDailyQuotationSummary();

      if (response.success && showToast) {
        toast({
          title: 'Resumen diario enviado',
          description: 'El resumen diario ha sido enviado a Slack exitosamente',
        });
      } else if (!response.success && showToast) {
        toast({
          title: 'Error en resumen diario',
          description: response.error || 'No se pudo enviar el resumen diario',
          variant: 'destructive',
        });
      }

      return response;
    } catch (error) {
      console.error('Error sending daily summary:', error);

      if (showToast) {
        toast({
          title: 'Error inesperado',
          description: 'Error inesperado al enviar resumen diario',
          variant: 'destructive',
        });
      }

      return {
        success: false,
        error: 'Error inesperado al enviar resumen',
      };
    } finally {
      setIsSending(false);
    }
  }, [toast]);

  /**
   * Automatically send notification when quotation is created for fleet
   */
  const autoNotifyQuotationCreated = useCallback(async (
    order: CorrectiveMaintenanceOrder,
    quotation: Quotation
  ): Promise<void> => {
    // Only send automatic notifications for fleet quotations
    if (quotation.approvalType === 'fleet') {
      await notifyFleetQuotation(order, quotation, 'pending', false);
    }
  }, [notifyFleetQuotation]);

  /**
   * Automatically send notification when quotation is approved/rejected
   */
  const autoNotifyQuotationDecision = useCallback(async (
    order: CorrectiveMaintenanceOrder,
    quotation: Quotation,
    decision: 'approved' | 'rejected'
  ): Promise<void> => {
    // Only send automatic notifications for fleet quotations
    if (quotation.approvalType === 'fleet') {
      await notifyFleetQuotation(order, quotation, decision, false);
    }
  }, [notifyFleetQuotation]);

  /**
   * Check if notifications should be sent for this quotation
   */
  const shouldNotify = useCallback((quotation: Quotation): boolean => {
    return quotation.approvalType === 'fleet';
  }, []);

  /**
   * Get notification preview for testing
   */
  const getNotificationPreview = useCallback((
    order: CorrectiveMaintenanceOrder,
    quotation: Quotation,
    type: 'pending' | 'approved' | 'rejected' = 'pending'
  ) => {
    const vehicleInfo = order.vehicle
      ? `${order.vehicle.brand} ${order.vehicle.model} - ${order.vehicle.carPlates?.plates}`
      : 'Vehículo no especificado';

    const totalCost = quotation.services.reduce((sum, service) => sum + service.estimatedCost, 0);
    const orderNumber = order._id.slice(-8).toUpperCase();

    return {
      title: `🔔 Nueva Cotización Pendiente - Orden #${orderNumber}`,
      message: `Se requiere aprobación para una cotización de ${new Intl.NumberFormat('es-MX', {
        style: 'currency',
        currency: 'MXN',
      }).format(totalCost)} para el vehículo ${vehicleInfo}`,
      details: {
        vehicleInfo,
        workshopName: order.workshop?.name || 'Taller no especificado',
        totalCost,
        serviceCount: quotation.services.length,
        estimatedDuration: quotation.services.reduce((sum, service) => sum + service.estimatedDuration, 0),
        customerName: order.associate?.name || 'Cliente no especificado',
        approvalType: quotation.approvalType,
      },
      shouldSend: quotation.approvalType === 'fleet',
    };
  }, []);

  return {
    // State
    isSending,

    // Actions
    notifyFleetQuotation,
    notifyServiceCompletion,
    sendDailySummary,

    // Auto notifications
    autoNotifyQuotationCreated,
    autoNotifyQuotationDecision,

    // Utilities
    shouldNotify,
    getNotificationPreview,
  };
}
