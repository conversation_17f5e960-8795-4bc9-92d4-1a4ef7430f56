'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse, Quotation } from '../types';

export async function escalateToFleet(
  quotationId: string
): Promise<ApiResponse<Quotation>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Escalating quotation to fleet:', quotationId);

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/quotations/${quotationId}/escalate-to-fleet`,
      {
        escalatedBy: user._id,
        escalatedAt: new Date().toISOString(),
        reason: 'Customer approval timeout'
      },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Quotation escalated to fleet successfully:', response.data);

    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Cotización escalada a fleet exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error escalating quotation to fleet:', error);

    const errorMessage = error.response?.data?.message ||
                        error.message ||
                        'Error al escalar la cotización a fleet';

    return {
      success: false,
      data: null as any,
      message: errorMessage,
    };
  }
}

export async function sendCustomerReminder(
  quotationId: string
): Promise<ApiResponse<any>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Sending customer reminder for quotation:', quotationId);

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/quotations/${quotationId}/send-reminder`,
      {
        sentBy: user._id,
        sentAt: new Date().toISOString(),
        reminderType: 'approval_pending'
      },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Customer reminder sent successfully:', response.data);

    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Recordatorio enviado al cliente exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error sending customer reminder:', error);

    const errorMessage = error.response?.data?.message ||
                        error.message ||
                        'Error al enviar recordatorio al cliente';

    return {
      success: false,
      data: null as any,
      message: errorMessage,
    };
  }
}

export async function setCustomerApprovalTimeout(
  quotationId: string,
  timeoutHours: number = 24
): Promise<ApiResponse<Quotation>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Setting customer approval timeout:', quotationId, timeoutHours);

    const deadline = new Date();
    deadline.setHours(deadline.getHours() + timeoutHours);

    const response = await axios.patch(
      `${URL_API}/vendor-platform/corrective-maintenance/quotations/${quotationId}/set-timeout`,
      {
        customerApprovalDeadline: deadline.toISOString(),
        customerResponseTimeout: timeoutHours,
        updatedBy: user._id
      },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Customer approval timeout set successfully:', response.data);

    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Tiempo límite de aprobación establecido exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error setting customer approval timeout:', error);

    const errorMessage = error.response?.data?.message ||
                        error.message ||
                        'Error al establecer tiempo límite de aprobación';

    return {
      success: false,
      data: null as any,
      message: errorMessage,
    };
  }
}

export async function checkAndAutoEscalate(): Promise<ApiResponse<any>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Checking for quotations to auto-escalate');

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/quotations/auto-escalate`,
      {
        checkedBy: user._id,
        checkedAt: new Date().toISOString()
      },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Auto-escalation check completed:', response.data);

    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Verificación de auto-escalación completada',
    };
  } catch (error: any) {
    console.error('❌ Error in auto-escalation check:', error);

    const errorMessage = error.response?.data?.message ||
                        error.message ||
                        'Error en verificación de auto-escalación';

    return {
      success: false,
      data: null as any,
      message: errorMessage,
    };
  }
}
