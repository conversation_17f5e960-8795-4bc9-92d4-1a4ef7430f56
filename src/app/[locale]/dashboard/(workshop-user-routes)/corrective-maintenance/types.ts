// Re-export types from the service for convenience
export type {
  CorrectiveMaintenanceOrder,
  CorrectiveService,
  ServicePart,
  Evidence,
  Diagnosis,
  Quotation,
  QuotationService,
  CreateOrderRequest,
  CompleteDiagnosisRequest,
  CreateQuotationRequest,
  ProcessApprovalRequest,
  UpdateServiceProgressRequest,
  ApiResponse
} from '@/constants/correctiveMaintenanceService';

// Import the type for use in utility functions
import type { CorrectiveService } from '@/constants/correctiveMaintenanceService';

// Additional UI-specific types
export interface OrderFilters {
  status?: string;
  workshopId?: string;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  page?: string;
  limit?: string;
}

export interface ServiceTypeOption {
  value: string;
  label: string;
  category: string;
}

export const SERVICE_TYPES: ServiceTypeOption[] = [
  { value: 'brakes', label: 'Frenos', category: 'Sistema de Frenos' },
  { value: 'tires', label: 'Llantas', category: 'Neumáticos' },
  { value: 'suspension', label: 'Suspensión', category: 'Sistema de Suspensión' },
  { value: 'engine', label: 'Motor', category: 'Sistema del Motor' },
  { value: 'transmission', label: 'Transmisión', category: 'Sistema de Transmisión' },
  { value: 'electrical', label: 'Sistema Eléctrico', category: 'Eléctrico' },
  { value: 'cooling-system', label: 'Aire Acondicionado', category: 'Climatización' },
  { value: 'bodywork', label: 'Carrocería', category: 'Carrocería' },
  { value: 'clutch', label: 'Clutch', category: 'Sistema de Transmisión' },
  { value: 'exhaust', label: 'Sistema de Escape', category: 'Sistema del Motor' },
  { value: 'fuel-system', label: 'Sistema de Combustible', category: 'Sistema del Motor' },
  { value: 'steering', label: 'Dirección', category: 'Sistema de Dirección' },
  { value: 'cooling-system', label: 'Sistema de Enfriamiento', category: 'Sistema del Motor' },
  { value: 'other', label: 'Otro', category: 'Otros' },
];

export const ORDER_STATUS_LABELS = {
  'pending': 'Pendiente',
  'diagnosed': 'Diagnosticado',
  'quoted': 'Cotizado',
  'approved': 'Aprobado',
  'in-progress': 'En Progreso',
  'waiting-for-parts': 'Esperando Refacciones',
  'completed': 'Completado',
  'cancelled': 'Cancelado',
};

export const SERVICE_STATUS_LABELS = {
  'not-started': 'No Iniciado',
  'in-progress': 'En Progreso',
  'completed': 'Completado',
  'waiting-for-parts': 'Esperando Refacciones',
  'cancelled': 'Cancelado',
};

export const FAILURE_TYPE_LABELS = {
  'known': 'Falla Conocida',
  'unknown': 'Requiere Diagnóstico',
};

export const ARRIVAL_METHOD_LABELS = {
  'driving': 'Llega Rodando',
  'tow-truck': 'Requiere Grúa',
};

export const APPROVAL_TYPE_LABELS = {
  'fleet': 'Aprobación de Flotillas',
  'customer': 'Aprobación del Cliente',
};

export const ORDER_TYPE_LABELS = {
  'customer-initiated': 'Iniciado por Cliente',
  'preventive-detected': 'Detectado en Mantenimiento Preventivo',
};

// Status color mappings for UI
export const ORDER_STATUS_COLORS = {
  'pending': 'bg-yellow-100 text-yellow-800',
  'diagnosed': 'bg-blue-100 text-blue-800',
  'quoted': 'bg-purple-100 text-purple-800',
  'approved': 'bg-green-100 text-green-800',
  'in-progress': 'bg-orange-100 text-orange-800',
  'waiting-for-parts': 'bg-amber-100 text-amber-800',
  'completed': 'bg-green-100 text-green-800',
  'cancelled': 'bg-red-100 text-red-800',
};

export const SERVICE_STATUS_COLORS = {
  'not-started': 'bg-gray-100 text-gray-800',
  'in-progress': 'bg-blue-100 text-blue-800',
  'completed': 'bg-green-100 text-green-800',
  'waiting-for-parts': 'bg-yellow-100 text-yellow-800',
  'cancelled': 'bg-red-100 text-red-800',
};

// Form validation schemas (using Yup)
export const createOrderSchema = {
  stockId: 'required',
  associateId: 'required',
  workshopId: 'required',
  type: 'required',
  failureType: 'required',
  arrivalMethod: 'required',
  customerDescription: 'required|min:10',
  canVehicleDrive: 'boolean',
  needsTowTruck: 'boolean',
  approvalType: 'required',
};

export const diagnosisSchema = {
  diagnosisNotes: 'required|min:20',
  services: 'required|array|min:1',
};

export const serviceSchema = {
  serviceType: 'required',
  serviceName: 'required|min:3',
  description: 'required|min:10',
  estimatedCost: 'required|numeric|min:1',
  estimatedDuration: 'required|numeric|min:0.5',
};

// Utility functions
export const getStatusLabel = (status: string, type: 'order' | 'service') => {
  if (type === 'order') {
    return ORDER_STATUS_LABELS[status as keyof typeof ORDER_STATUS_LABELS] || status;
  }
  return SERVICE_STATUS_LABELS[status as keyof typeof SERVICE_STATUS_LABELS] || status;
};

export const getStatusColor = (status: string, type: 'order' | 'service') => {
  if (type === 'order') {
    return ORDER_STATUS_COLORS[status as keyof typeof ORDER_STATUS_COLORS] || 'bg-gray-100 text-gray-800';
  }
  return SERVICE_STATUS_COLORS[status as keyof typeof SERVICE_STATUS_COLORS] || 'bg-gray-100 text-gray-800';
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  }).format(amount);
};

export const formatDuration = (hours: number) => {
  if (hours < 1) {
    return `${Math.round(hours * 60)} min`;
  }
  if (hours === 1) {
    return '1 hora';
  }
  if (hours < 24) {
    return `${hours} horas`;
  }
  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  if (remainingHours === 0) {
    return `${days} ${days === 1 ? 'día' : 'días'}`;
  }
  return `${days} ${days === 1 ? 'día' : 'días'} ${remainingHours} horas`;
};

export const calculateTotalCost = (services: CorrectiveService[]) => {
  return services.reduce((total, service) => total + service.estimatedCost, 0);
};

export const calculateTotalDuration = (services: CorrectiveService[]) => {
  return services.reduce((total, service) => total + service.estimatedDuration, 0);
};

export const getServicesByStatus = (services: CorrectiveService[], status: string) => {
  return services.filter(service => service.status === status);
};

export const isOrderEditable = (status: string) => {
  return ['pending', 'diagnosed'].includes(status);
};

export const canStartWork = (status: string) => {
  return status === 'approved';
};

export const canContinueWork = (status: string) => {
  return ['approved', 'waiting-for-parts'].includes(status);
};

export const canCompleteOrder = (services: CorrectiveService[]) => {
  return services.length > 0 && services.every(service =>
    service.status === 'completed' || service.status === 'cancelled'
  );
};
