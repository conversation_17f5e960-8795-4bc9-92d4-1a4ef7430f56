'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Clock, AlertTriangle, Users, User, TrendingUp } from 'lucide-react';
import { Quotation } from '../types';
import { useApprovalEscalation } from '../_hooks/useApprovalEscalation';

interface ApprovalStatusWidgetProps {
  quotations: Quotation[];
  onQuotationUpdate: (quotationId: string, updates: Partial<Quotation>) => void;
  onViewDetails?: () => void;
}

export default function ApprovalStatusWidget({
  quotations,
  onQuotationUpdate,
  onViewDetails
}: ApprovalStatusWidgetProps) {
  const {
    pendingCustomerQuotations,
    overdueCustomerQuotations,
    approachingDeadlineQuotations,
    stats
  } = useApprovalEscalation({
    quotations,
    onQuotationUpdate,
    autoEscalateEnabled: true,
    checkIntervalMinutes: 5
  });

  const escalatedQuotations = quotations.filter(q => 
    q.status === 'escalated-to-fleet' || 
    (q.approvalType === 'fleet' && q.escalatedToFleetAt)
  );

  const getStatusColor = (count: number, type: 'pending' | 'overdue' | 'approaching' | 'escalated') => {
    if (count === 0) return 'text-gray-500';
    
    switch (type) {
      case 'overdue':
        return 'text-red-600';
      case 'approaching':
        return 'text-orange-600';
      case 'escalated':
        return 'text-blue-600';
      default:
        return 'text-green-600';
    }
  };

  const getBadgeVariant = (count: number, type: 'pending' | 'overdue' | 'approaching' | 'escalated') => {
    if (count === 0) return 'outline';
    
    switch (type) {
      case 'overdue':
        return 'destructive';
      case 'approaching':
        return 'secondary';
      case 'escalated':
        return 'default';
      default:
        return 'secondary';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Estado de Aprobaciones
          </div>
          {onViewDetails && (
            <Button variant="outline" size="sm" onClick={onViewDetails}>
              Ver Detalles
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <User className={`h-4 w-4 ${getStatusColor(stats.totalPending, 'pending')}`} />
              <Badge variant={getBadgeVariant(stats.totalPending, 'pending')}>
                {stats.totalPending}
              </Badge>
            </div>
            <p className="text-xs text-gray-600">Pendientes Cliente</p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <AlertTriangle className={`h-4 w-4 ${getStatusColor(stats.totalOverdue, 'overdue')}`} />
              <Badge variant={getBadgeVariant(stats.totalOverdue, 'overdue')}>
                {stats.totalOverdue}
              </Badge>
            </div>
            <p className="text-xs text-gray-600">Vencidas</p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <Clock className={`h-4 w-4 ${getStatusColor(stats.totalApproachingDeadline, 'approaching')}`} />
              <Badge variant={getBadgeVariant(stats.totalApproachingDeadline, 'approaching')}>
                {stats.totalApproachingDeadline}
              </Badge>
            </div>
            <p className="text-xs text-gray-600">Por Vencer</p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-1">
              <Users className={`h-4 w-4 ${getStatusColor(escalatedQuotations.length, 'escalated')}`} />
              <Badge variant={getBadgeVariant(escalatedQuotations.length, 'escalated')}>
                {escalatedQuotations.length}
              </Badge>
            </div>
            <p className="text-xs text-gray-600">En Fleet</p>
          </div>
        </div>

        {/* Recent Activity */}
        {(overdueCustomerQuotations.length > 0 || approachingDeadlineQuotations.length > 0) && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Actividad Reciente</h4>
            
            {overdueCustomerQuotations.slice(0, 2).map(quotation => (
              <div key={quotation._id} className="flex items-center justify-between p-2 bg-red-50 rounded-lg border border-red-200">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800">
                    {quotation.quotationNumber}
                  </span>
                  <Badge variant="destructive" className="text-xs">
                    Vencida
                  </Badge>
                </div>
                <span className="text-xs text-red-600">
                  Auto-escalando...
                </span>
              </div>
            ))}

            {approachingDeadlineQuotations.slice(0, 2).map(quotation => (
              <div key={quotation._id} className="flex items-center justify-between p-2 bg-orange-50 rounded-lg border border-orange-200">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-medium text-orange-800">
                    {quotation.quotationNumber}
                  </span>
                  <Badge variant="secondary" className="text-xs">
                    Por vencer
                  </Badge>
                </div>
                <span className="text-xs text-orange-600">
                  {quotation.customerApprovalDeadline && 
                    new Date(quotation.customerApprovalDeadline).toLocaleTimeString()
                  }
                </span>
              </div>
            ))}
          </div>
        )}

        {/* System Status */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
          <span>Sistema de escalación automática activo</span>
          <span>Última verificación: {stats.lastCheckTime.toLocaleTimeString()}</span>
        </div>

        {/* No activity message */}
        {stats.totalPending === 0 && stats.totalOverdue === 0 && escalatedQuotations.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            <div className="flex items-center justify-center gap-2 mb-2">
              <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm">Todo al día</span>
            </div>
            <p className="text-xs">No hay cotizaciones pendientes de gestión</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
