'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Upload, X, Image, Video, FileText, Camera } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CorrectiveService, formatCurrency, formatDuration } from '../types';
import { completeServiceWithEvidence } from '../_actions/updateServiceExecution';

const serviceEvidenceSchema = z.object({
  completionNotes: z.string().min(10, 'Las notas de finalización deben tener al menos 10 caracteres'),
  workPerformed: z.string().min(10, 'Descripción del trabajo realizado requerida'),
  evidence: z.array(z.instanceof(File)).min(1, 'Debe subir al menos un archivo de evidencia'),
  recommendations: z.string().optional(),
  nextMaintenanceDate: z.string().optional(),
  partsUsed: z.string().optional(),
});

type ServiceEvidenceFormData = z.infer<typeof serviceEvidenceSchema>;

interface ServiceEvidenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  service: CorrectiveService;
  orderId: string;
}

export default function ServiceEvidenceModal({
  isOpen,
  onClose,
  onSuccess,
  service,
  orderId
}: ServiceEvidenceModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<Record<string, string>>({});

  const form = useForm<ServiceEvidenceFormData>({
    resolver: zodResolver(serviceEvidenceSchema),
    defaultValues: {
      completionNotes: '',
      workPerformed: '',
      evidence: [],
      recommendations: '',
      nextMaintenanceDate: '',
      partsUsed: '',
    },
  });

  const onSubmit = async (data: ServiceEvidenceFormData) => {
    setIsSubmitting(true);
    try {
      const completionData = {
        completionNotes: data.completionNotes,
        workPerformed: data.workPerformed,
        evidence: selectedFiles,
        recommendations: data.recommendations,
        nextMaintenanceDate: data.nextMaintenanceDate,
        partsUsed: data.partsUsed,
        completedAt: new Date().toISOString(),
      };

      const response = await completeServiceWithEvidence(service._id, orderId, completionData);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Servicio completado con evidencia exitosamente',
        });

        form.reset();
        setSelectedFiles([]);
        setPreviewUrls({});
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al completar el servicio',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error completing service with evidence:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al completar el servicio',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const newFiles = [...selectedFiles, ...files];
    setSelectedFiles(newFiles);

    // Create preview URLs for images
    files.forEach((file) => {
      if (file.type.startsWith('image/')) {
        const url = URL.createObjectURL(file);
        setPreviewUrls(prev => ({ ...prev, [file.name]: url }));
      }
    });

    // Update form
    form.setValue('evidence', newFiles);
  };

  const removeFile = (index: number) => {
    const fileToRemove = selectedFiles[index];
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(newFiles);

    // Remove preview URL
    if (previewUrls[fileToRemove.name]) {
      URL.revokeObjectURL(previewUrls[fileToRemove.name]);
      setPreviewUrls(prev => {
        const newUrls = { ...prev };
        delete newUrls[fileToRemove.name];
        return newUrls;
      });
    }

    form.setValue('evidence', newFiles);
  };

  const handleClose = () => {
    // Clean up preview URLs
    Object.values(previewUrls).forEach(url => URL.revokeObjectURL(url));

    form.reset();
    setSelectedFiles([]);
    setPreviewUrls({});
    onClose();
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (file.type.startsWith('video/')) return <Video className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Completar Servicio con Evidencia</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Service Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{service.serviceName}</span>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  Completando
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">{service.description}</p>
            </CardHeader>
            <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Costo Estimado:</p>
                <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
              </div>
              <div>
                <p className="text-gray-500">Duración Estimada:</p>
                <p className="font-medium">{formatDuration(service.estimatedDuration)}</p>
              </div>
              <div>
                <p className="text-gray-500">Refacciones:</p>
                <p className="font-medium">{service.parts.length}</p>
              </div>
            </CardContent>
          </Card>

          {/* Work Performed */}
          <div className="space-y-2">
            <Label htmlFor="workPerformed">Trabajo Realizado *</Label>
            <Textarea
              {...form.register('workPerformed')}
              placeholder="Describe detalladamente el trabajo que se realizó en este servicio..."
              rows={4}
            />
            {form.formState.errors.workPerformed && (
              <p className="text-sm text-red-500">{form.formState.errors.workPerformed.message}</p>
            )}
          </div>

          {/* Completion Notes */}
          <div className="space-y-2">
            <Label htmlFor="completionNotes">Notas de Finalización *</Label>
            <Textarea
              {...form.register('completionNotes')}
              placeholder="Notas sobre la finalización del servicio, resultados obtenidos, etc..."
              rows={3}
            />
            {form.formState.errors.completionNotes && (
              <p className="text-sm text-red-500">{form.formState.errors.completionNotes.message}</p>
            )}
          </div>

          {/* Parts Used */}
          <div className="space-y-2">
            <Label htmlFor="partsUsed">Refacciones Utilizadas</Label>
            <Textarea
              {...form.register('partsUsed')}
              placeholder="Lista las refacciones que se utilizaron en este servicio..."
              rows={2}
            />
          </div>

          {/* Recommendations */}
          <div className="space-y-2">
            <Label htmlFor="recommendations">Recomendaciones</Label>
            <Textarea
              {...form.register('recommendations')}
              placeholder="Recomendaciones para el cliente o próximos mantenimientos..."
              rows={2}
            />
          </div>

          {/* Next Maintenance Date */}
          <div className="space-y-2">
            <Label htmlFor="nextMaintenanceDate">Próximo Mantenimiento Sugerido</Label>
            <input
              type="date"
              {...form.register('nextMaintenanceDate')}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>

          {/* Evidence Upload */}
          <div className="space-y-4">
            <Label>Evidencia del Trabajo Realizado *</Label>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <Camera className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="evidence-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Subir fotos y videos del trabajo realizado
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      PNG, JPG, MP4 hasta 10MB cada uno (Mínimo 1 archivo)
                    </span>
                  </label>
                  <input
                    id="evidence-upload"
                    type="file"
                    multiple
                    accept="image/*,video/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            </div>

            {form.formState.errors.evidence && (
              <p className="text-sm text-red-500">{form.formState.errors.evidence.message}</p>
            )}

            {/* Selected Files */}
            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <Label>Archivos Seleccionados ({selectedFiles.length})</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="relative border rounded-lg p-3 bg-gray-50">
                      {/* Preview for images */}
                      {file.type.startsWith('image/') && previewUrls[file.name] && (
                        <div className="mb-2">
                          <img
                            src={previewUrls[file.name]}
                            alt={file.name}
                            className="w-full h-20 object-cover rounded"
                          />
                        </div>
                      )}

                      <div className="flex items-center space-x-2">
                        {getFileIcon(file)}
                        <div className="flex-1 min-w-0">
                          <p className="text-xs font-medium truncate">{file.name}</p>
                          <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(index)}
                          className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting || selectedFiles.length === 0}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Completar Servicio
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
