'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Upload, Clock, CheckCircle, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CorrectiveService, SERVICE_STATUS_LABELS, getStatusColor, formatCurrency, formatDuration } from '../types';

// Base schema for all status updates
const baseProgressSchema = z.object({
  status: z.enum(['not-started', 'in-progress', 'completed', 'waiting-for-parts', 'cancelled']),
  notes: z.string().optional(),
  evidence: z.array(z.instanceof(File)).optional(),
});

// Schema for completed status - evidence is required
const completedProgressSchema = z.object({
  status: z.enum(['not-started', 'in-progress', 'completed', 'waiting-for-parts', 'cancelled']),
  notes: z.string().optional(),
  evidence: z.array(z.instanceof(File)).min(1, 'La evidencia es obligatoria cuando el servicio se marca como completado'),
});

type ProgressFormData = z.infer<typeof baseProgressSchema>;

interface ServiceProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  serviceId: string;
  service?: CorrectiveService;
}

export default function ServiceProgressModal({
  isOpen,
  onClose,
  onSuccess,
  serviceId,
  service
}: ServiceProgressModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const form = useForm<ProgressFormData>({
    resolver: zodResolver(baseProgressSchema),
    defaultValues: {
      status: service?.status || 'not-started',
      notes: '',
      evidence: [],
    },
  });

  const watchedStatus = form.watch('status');

  // Update form validation when status changes
  React.useEffect(() => {
    const currentSchema = watchedStatus === 'completed' ? completedProgressSchema : baseProgressSchema;
    form.clearErrors();
    // Update the evidence field in form data for validation
    form.setValue('evidence', selectedFiles);

    // Re-validate with the new schema if status is completed
    if (watchedStatus === 'completed') {
      // Update the resolver to use the completed schema
      const newResolver = zodResolver(currentSchema);
      form.trigger();
    }
  }, [watchedStatus, selectedFiles, form]);

  // Validate evidence when files change and status is completed
  React.useEffect(() => {
    if (watchedStatus === 'completed') {
      form.setValue('evidence', selectedFiles);
      form.trigger('evidence');
    }
  }, [selectedFiles, watchedStatus, form]);

  const onSubmit = async (data: ProgressFormData) => {
    // Validate evidence requirement for completed status
    if (data.status === 'completed' && selectedFiles.length === 0) {
      form.setError('evidence', {
        type: 'manual',
        message: 'La evidencia es obligatoria cuando el servicio se marca como completado'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Use fetch directly instead of Server Action to handle FormData properly
      const formData = new FormData();
      formData.append('status', data.status);

      if (data.notes) {
        formData.append('notes', data.notes);
      }

      // Add evidence files
      selectedFiles.forEach((file) => {
        formData.append('evidence', file);
      });

      console.log('📤 Submitting service progress update:', {
        serviceId,
        status: data.status,
        notesLength: data.notes?.length || 0,
        evidenceCount: selectedFiles.length,
        evidenceFiles: selectedFiles.map(f => ({ name: f.name, size: f.size, type: f.type }))
      });

      const userResponse = await fetch('/api/auth/user');
      const user = await userResponse.json();

      if (!userResponse.ok || !user.success || !user.accessToken) {
        console.error('❌ Error getting user token:', user);
        toast({
          title: 'Error de autenticación',
          description: 'No se pudo obtener el token de autenticación. Por favor, inicia sesión nuevamente.',
          variant: 'destructive',
        });
        return;
      }

      console.log('🔑 User token obtained successfully');

      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/progress`, {
        method: 'PATCH',
        body: formData,
        headers: {
          'Authorization': `Bearer ${user.accessToken}`,
        },
      });

      const responseData = await response.json();

      console.log('📥 Service progress update response:', {
        status: response.status,
        ok: response.ok,
        data: responseData
      });

      if (response.ok && responseData.success !== false) {
        console.log('✅ Service progress updated successfully!');

        // Show success message with more details
        const evidenceCount = selectedFiles.length;
        const statusText = data.status === 'completed' ? 'completado' : 'actualizado';

        toast({
          title: '✅ Éxito',
          description: `Servicio ${statusText} exitosamente${evidenceCount > 0 ? ` con ${evidenceCount} archivo${evidenceCount !== 1 ? 's' : ''} de evidencia` : ''}`,
        });

        // Reset form and close modal
        form.reset();
        setSelectedFiles([]);

        // Call onSuccess to refresh the parent component
        console.log('🔄 Calling onSuccess to refresh data...');

        // Close modal immediately and let onSuccess handle the refresh
        handleClose();

        // Call onSuccess after closing to refresh the parent component
        setTimeout(() => {
          onSuccess();
        }, 100);

      } else {
        console.error('❌ Service progress update failed:', responseData);
        toast({
          title: 'Error',
          description: responseData.message || 'Error al actualizar el progreso del servicio',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('❌ Error updating service progress:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al actualizar el progreso del servicio',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);

    // Validate file types and sizes
    const validFiles = files.filter(file => {
      const isValidType = file.type.startsWith('image/') || file.type.startsWith('video/');
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB

      if (!isValidType) {
        toast({
          title: 'Tipo de archivo no válido',
          description: `${file.name} no es un archivo de imagen o video válido`,
          variant: 'destructive',
        });
        return false;
      }

      if (!isValidSize) {
        toast({
          title: 'Archivo muy grande',
          description: `${file.name} excede el tamaño máximo de 10MB`,
          variant: 'destructive',
        });
        return false;
      }

      return true;
    });

    console.log('📁 Files selected:', {
      total: files.length,
      valid: validFiles.length,
      files: validFiles.map(f => ({ name: f.name, size: f.size, type: f.type }))
    });

    setSelectedFiles(prev => [...prev, ...validFiles]);

    // Clear the input so the same file can be selected again if needed
    event.target.value = '';
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    form.reset();
    setSelectedFiles([]);
    onClose();
  };

  // Test function to verify the endpoint is working
  const testEndpoint = async () => {
    try {
      console.log('🧪 Testing endpoint connectivity...');

      const userResponse = await fetch('/api/auth/user');
      const user = await userResponse.json();

      if (!userResponse.ok || !user.success) {
        console.error('❌ Auth test failed:', user);
        toast({
          title: 'Test Failed',
          description: 'Authentication test failed',
          variant: 'destructive',
        });
        return;
      }

      console.log('✅ Auth test passed');

      // Test with minimal data
      const testFormData = new FormData();
      testFormData.append('status', 'in-progress');
      testFormData.append('notes', 'Test from frontend');

      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/progress`, {
        method: 'PATCH',
        body: testFormData,
        headers: {
          'Authorization': `Bearer ${user.accessToken}`,
        },
      });

      const responseData = await response.json();

      console.log('🧪 Test response:', {
        status: response.status,
        ok: response.ok,
        data: responseData
      });

      toast({
        title: response.ok ? 'Test Passed' : 'Test Failed',
        description: response.ok ? 'Endpoint is working correctly' : `Error: ${responseData.message}`,
        variant: response.ok ? 'default' : 'destructive',
      });

    } catch (error) {
      console.error('❌ Test error:', error);
      toast({
        title: 'Test Error',
        description: 'Unexpected error during test',
        variant: 'destructive',
      });
    }
  };

  const getProgressPercentage = (status: string) => {
    switch (status) {
      case 'not-started': return 0;
      case 'in-progress': return 50;
      case 'waiting-for-parts': return 25;
      case 'completed': return 100;
      case 'cancelled': return 0;
      default: return 0;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'not-started': return <Clock className="h-4 w-4" />;
      case 'in-progress': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'waiting-for-parts': return <AlertTriangle className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  if (!service) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Servicio no encontrado</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-600">
              No se pudo cargar la información del servicio.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={handleClose}>Cerrar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Actualizar Progreso del Servicio</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Service Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{service.serviceName}</span>
                <Badge className={getStatusColor(service.status, 'service')} variant="secondary">
                  {SERVICE_STATUS_LABELS[service.status as keyof typeof SERVICE_STATUS_LABELS]}
                </Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">{service.description}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Costo Estimado:</p>
                  <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Duración Estimada:</p>
                  <p className="font-medium">{formatDuration(service.estimatedDuration)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Refacciones:</p>
                  <p className="font-medium">{service.parts.length}</p>
                </div>
              </div>

              {/* Current Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Progreso Actual</Label>
                  <span className="text-sm text-gray-500">
                    {getProgressPercentage(service.status)}%
                  </span>
                </div>
                <Progress value={getProgressPercentage(service.status)} className="h-2" />
              </div>
            </CardContent>
          </Card>

          {/* Status Update */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Actualizar Estado</h3>

            <div className="space-y-2">
              <Label htmlFor="status">Nuevo Estado *</Label>
              <Select
                value={watchedStatus}
                onValueChange={(value: any) => form.setValue('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona un estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="not-started">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      No Iniciado
                    </div>
                  </SelectItem>
                  <SelectItem value="in-progress">
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4" />
                      En Progreso
                    </div>
                  </SelectItem>
                  <SelectItem value="waiting-for-parts">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Esperando Refacciones
                    </div>
                  </SelectItem>
                  <SelectItem value="completed">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4" />
                      Completado
                    </div>
                  </SelectItem>
                  <SelectItem value="cancelled">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4" />
                      Cancelado
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              {form.formState.errors.status && (
                <p className="text-sm text-red-500">{form.formState.errors.status.message}</p>
              )}
            </div>

            {/* Progress Preview */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-3">
                  <div className={getStatusColor(watchedStatus, 'service').split(' ')[0] + ' p-2 rounded-full'}>
                    {getStatusIcon(watchedStatus)}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">
                      {SERVICE_STATUS_LABELS[watchedStatus as keyof typeof SERVICE_STATUS_LABELS]}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <Progress value={getProgressPercentage(watchedStatus)} className="h-2 flex-1" />
                      <span className="text-sm text-gray-500">
                        {getProgressPercentage(watchedStatus)}%
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notas del Progreso</Label>
            <Textarea
              {...form.register('notes')}
              placeholder="Describe el progreso realizado, problemas encontrados, etc..."
              rows={4}
            />
            {form.formState.errors.notes && (
              <p className="text-sm text-red-500">{form.formState.errors.notes.message}</p>
            )}
          </div>

          {/* Evidence Upload - Only show when status is "completed" */}
          {watchedStatus === 'completed' && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  Evidencia (Fotos/Videos) *
                  <span className="text-red-500 text-sm">(Obligatorio para completar)</span>
                </Label>
                {form.formState.errors.evidence && (
                  <p className="text-sm text-red-500">{form.formState.errors.evidence.message}</p>
                )}
              </div>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                <div className="text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <label htmlFor="evidence-upload" className="cursor-pointer">
                      <span className="mt-2 block text-sm font-medium text-gray-900">
                        Subir archivos de evidencia
                      </span>
                      <span className="mt-1 block text-sm text-gray-500">
                        PNG, JPG, MP4 hasta 10MB cada uno (Obligatorio)
                      </span>
                    </label>
                    <input
                      id="evidence-upload"
                      type="file"
                      multiple
                      accept="image/*,video/*"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </div>
                </div>
              </div>

              {/* Selected Files */}
              {selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <Label>Archivos Seleccionados</Label>
                  <div className="space-y-2">
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm truncate">{file.name}</span>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeFile(index)}
                        >
                          Eliminar
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Information about evidence requirement */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800">
                      Evidencia Requerida
                    </p>
                    <p className="text-sm text-yellow-700 mt-1">
                      Para marcar un servicio como completado, es obligatorio subir evidencia fotográfica o en video del trabajo realizado.
                    </p>
                    {selectedFiles.length === 0 && (
                      <p className="text-sm text-red-600 mt-2 font-medium">
                        ⚠️ Debe seleccionar al menos un archivo de evidencia antes de completar el servicio.
                      </p>
                    )}
                    {selectedFiles.length > 0 && (
                      <p className="text-sm text-green-600 mt-2 font-medium">
                        ✅ {selectedFiles.length} archivo{selectedFiles.length !== 1 ? 's' : ''} seleccionado{selectedFiles.length !== 1 ? 's' : ''} para evidencia.
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-between">
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || (watchedStatus === 'completed' && selectedFiles.length === 0)}
                className={watchedStatus === 'completed' && selectedFiles.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {watchedStatus === 'completed' ? 'Completar Servicio' : 'Actualizar Progreso'}
                {watchedStatus === 'completed' && selectedFiles.length === 0 && (
                  <span className="ml-2 text-xs">(Evidencia requerida)</span>
                )}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
