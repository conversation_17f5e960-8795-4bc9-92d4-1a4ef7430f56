'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CorrectiveMaintenanceOrder, Quotation } from '../types';

export interface SlackNotificationPayload {
  type: 'quotation_pending' | 'quotation_approved' | 'quotation_rejected' | 'service_completed';
  orderId: string;
  quotationId?: string;
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  details: {
    vehicleInfo?: string;
    workshopName?: string;
    totalCost?: number;
    serviceCount?: number;
    estimatedDuration?: number;
    customerName?: string;
    approvalType?: string;
  };
  actions?: {
    label: string;
    url: string;
    style?: 'primary' | 'danger' | 'default';
  }[];
  timestamp: string;
}

export interface SlackNotificationResponse {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Send notification to Slack for fleet quotations
 */
export async function sendFleetQuotationNotification(
  order: CorrectiveMaintenanceOrder,
  quotation: Quotation,
  type: 'pending' | 'approved' | 'rejected' = 'pending'
): Promise<SlackNotificationResponse> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      error: 'Usuario no autenticado',
    };
  }

  try {
    // Only send notifications for fleet approvals
    if (quotation.approvalType !== 'fleet') {
      return {
        success: true,
        messageId: 'skipped-not-fleet',
      };
    }

    const vehicleInfo = order.vehicle
      ? `${order.vehicle.brand} ${order.vehicle.model} - ${order.vehicle.carPlates?.plates}`
      : 'Vehículo no especificado';

    const totalCost = quotation.services.reduce((sum, service) => sum + service.estimatedCost, 0);
    const totalDuration = quotation.services.reduce((sum, service) => sum + service.estimatedDuration, 0);

    // Create notification payload
    const payload: SlackNotificationPayload = {
      type: type === 'pending' ? 'quotation_pending' : type === 'approved' ? 'quotation_approved' : 'quotation_rejected',
      orderId: order._id,
      quotationId: quotation._id,
      title: getNotificationTitle(type, order._id),
      message: getNotificationMessage(type, vehicleInfo, totalCost),
      priority: type === 'pending' ? 'high' : 'medium',
      details: {
        vehicleInfo,
        workshopName: order.workshop?.name || 'Taller no especificado',
        totalCost,
        serviceCount: quotation.services.length,
        estimatedDuration: totalDuration,
        customerName: order.associate?.name || 'Cliente no especificado',
        approvalType: quotation.approvalType,
      },
      actions: getNotificationActions(order._id, quotation._id, type),
      timestamp: new Date().toISOString(),
    };

    console.log('📤 Sending Slack notification:', payload);

    // Send to Slack via API
    const response = await axios.post(
      `${URL_API}/vendor-platform/notifications/slack/fleet-quotation`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Slack notification sent successfully:', response.data);

    return {
      success: true,
      messageId: response.data.messageId || response.data.ts,
    };

  } catch (error: any) {
    console.error('❌ Error sending Slack notification:', error);

    return {
      success: false,
      error: error.response?.data?.message || 'Error al enviar notificación a Slack',
    };
  }
}

/**
 * Send service completion notification to Slack
 */
export async function sendServiceCompletionNotification(
  order: CorrectiveMaintenanceOrder,
  serviceName: string,
  completionDetails: {
    actualDuration: number;
    estimatedDuration: number;
    totalCost: number;
    evidenceCount: number;
  }
): Promise<SlackNotificationResponse> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      error: 'Usuario no autenticado',
    };
  }

  try {
    const vehicleInfo = order.vehicle
      ? `${order.vehicle.brand} ${order.vehicle.model} - ${order.vehicle.carPlates?.plates}`
      : 'Vehículo no especificado';

    const isOverdue = completionDetails.actualDuration > completionDetails.estimatedDuration;
    const variance = completionDetails.actualDuration - completionDetails.estimatedDuration;

    const payload: SlackNotificationPayload = {
      type: 'service_completed',
      orderId: order._id,
      title: `✅ Servicio Completado - ${serviceName}`,
      message: `El servicio "${serviceName}" ha sido completado para ${vehicleInfo}`,
      priority: isOverdue ? 'medium' : 'low',
      details: {
        vehicleInfo,
        workshopName: order.workshop?.name || 'Taller no especificado',
        totalCost: completionDetails.totalCost,
        serviceCount: 1,
        estimatedDuration: completionDetails.estimatedDuration,
      },
      actions: [
        {
          label: 'Ver Orden Completa',
          url: `${process.env.NEXT_PUBLIC_APP_URL}/es/dashboard/corrective-maintenance/orders/${order._id}`,
          style: 'primary',
        },
      ],
      timestamp: new Date().toISOString(),
    };

    const response = await axios.post(
      `${URL_API}/vendor-platform/notifications/slack/service-completion`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      success: true,
      messageId: response.data.messageId || response.data.ts,
    };

  } catch (error: any) {
    console.error('❌ Error sending service completion notification:', error);

    return {
      success: false,
      error: error.response?.data?.message || 'Error al enviar notificación de finalización',
    };
  }
}

/**
 * Send daily summary of pending quotations to Slack
 */
export async function sendDailyQuotationSummary(): Promise<SlackNotificationResponse> {
  const user = await getCurrentUser();

  if (!user?.accessToken) {
    return {
      success: false,
      error: 'Usuario no autenticado',
    };
  }

  try {
    const response = await axios.post(
      `${URL_API}/vendor-platform/notifications/slack/daily-summary`,
      {
        type: 'daily_quotation_summary',
        timestamp: new Date().toISOString(),
      },
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    return {
      success: true,
      messageId: response.data.messageId || response.data.ts,
    };

  } catch (error: any) {
    console.error('❌ Error sending daily summary:', error);

    return {
      success: false,
      error: error.response?.data?.message || 'Error al enviar resumen diario',
    };
  }
}

// Helper functions
function getNotificationTitle(type: string, orderId: string): string {
  const orderNumber = orderId.slice(-8).toUpperCase();

  switch (type) {
    case 'pending':
      return `🔔 Nueva Cotización Pendiente - Orden #${orderNumber}`;
    case 'approved':
      return `✅ Cotización Aprobada - Orden #${orderNumber}`;
    case 'rejected':
      return `❌ Cotización Rechazada - Orden #${orderNumber}`;
    default:
      return `📋 Actualización de Cotización - Orden #${orderNumber}`;
  }
}

function getNotificationMessage(type: string, vehicleInfo: string, totalCost: number): string {
  const formattedCost = new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  }).format(totalCost);

  switch (type) {
    case 'pending':
      return `Se requiere aprobación para una cotización de ${formattedCost} para el vehículo ${vehicleInfo}`;
    case 'approved':
      return `La cotización de ${formattedCost} para ${vehicleInfo} ha sido aprobada`;
    case 'rejected':
      return `La cotización de ${formattedCost} para ${vehicleInfo} ha sido rechazada`;
    default:
      return `Actualización en la cotización de ${formattedCost} para ${vehicleInfo}`;
  }
}

function getNotificationActions(orderId: string, quotationId: string, type: string) {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

  if (type === 'pending') {
    return [
      {
        label: 'Revisar Cotización',
        url: `${baseUrl}/es/dashboard/corrective-maintenance/orders/${orderId}?tab=quotation&quotationId=${quotationId}`,
        style: 'primary' as const,
      },
      {
        label: 'Ver Orden Completa',
        url: `${baseUrl}/es/dashboard/corrective-maintenance/orders/${orderId}`,
        style: 'default' as const,
      },
    ];
  }

  return [
    {
      label: 'Ver Orden',
      url: `${baseUrl}/es/dashboard/corrective-maintenance/orders/${orderId}`,
      style: 'primary' as const,
    },
  ];
}
